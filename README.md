# Pet E-commerce Store

一个现代化的宠物用品电商网站，使用 Next.js 14+ 作为前端，WordPress + WooCommerce 作为后端 CMS。

## 技术栈

### 前端
- **Next.js 14+** - React 框架，使用 App Router
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Apollo Client** - GraphQL 客户端
- **Framer Motion** - 动画库
- **Zustand** - 状态管理

### 后端
- **WordPress 6.4+** - Headless CMS
- **WooCommerce** - 电商功能
- **WPGraphQL** - GraphQL API
- **JWT Authentication** - 用户认证

### 基础设施
- **Docker & Docker Compose** - 容器化部署
- **MySQL 8.0** - 数据库
- **Nginx** - 反向代理和负载均衡
- **Redis** - 缓存（可选）

## 快速开始

### 前置要求
- Docker 和 Docker Compose
- Node.js 18+ (用于本地开发)

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd pet-ecommerce-store
   ```

2. **环境配置**
   ```bash
   # 复制环境变量文件
   cp frontend/.env.local.example frontend/.env.local
   
   # 根据需要修改环境变量
   nano frontend/.env.local
   ```

3. **启动服务**
   ```bash
   # 启动所有服务
   docker-compose up -d
   
   # 查看服务状态
   docker-compose ps
   
   # 查看日志
   docker-compose logs -f
   ```

4. **访问应用**
   - 前端网站: http://localhost:3000
   - WordPress 管理后台: http://localhost/wp-admin
   - WordPress API: http://localhost/wp-json
   - GraphQL 端点: http://localhost/graphql

### 开发模式

如果你想在本地开发前端：

```bash
cd frontend
npm install
npm run dev
```

## 项目结构

```
pet-ecommerce-store/
├── docker-compose.yml          # Docker 服务配置
├── nginx/                      # Nginx 配置
│   └── nginx.conf
├── mysql/                      # MySQL 初始化脚本
│   └── init/
├── wordpress/                  # WordPress 配置
│   ├── themes/                # 自定义主题
│   ├── plugins/               # 自定义插件
│   └── php.ini               # PHP 配置
├── frontend/                   # Next.js 前端
│   ├── src/
│   │   ├── app/              # App Router 页面
│   │   ├── components/       # React 组件
│   │   ├── lib/              # 工具库
│   │   ├── hooks/            # 自定义 Hooks
│   │   ├── types/            # TypeScript 类型
│   │   └── store/            # 状态管理
│   ├── public/               # 静态资源
│   ├── package.json
│   ├── next.config.js
│   └── tailwind.config.js
└── README.md
```

## WordPress 配置

### 必需插件
- WooCommerce - 电商功能
- WPGraphQL - GraphQL API
- WooGraphQL - WooCommerce GraphQL 扩展
- JWT Authentication for WP-API - 用户认证

### 推荐插件
- Advanced Custom Fields (ACF) - 自定义字段
- Yoast SEO - SEO 优化
- WP Rocket - 缓存优化

## 开发指南

### 添加新页面
在 `frontend/src/app/` 目录下创建新的路由文件夹和 `page.tsx` 文件。

### 添加新组件
在 `frontend/src/components/` 目录下创建组件文件。

### API 集成
使用 Apollo Client 连接 WordPress GraphQL API，配置文件在 `frontend/src/lib/apollo.ts`。

### 样式开发
使用 Tailwind CSS 进行样式开发，自定义配置在 `frontend/tailwind.config.js`。

## 部署

### 生产环境部署
1. 修改 `docker-compose.yml` 中的环境变量
2. 配置域名和 SSL 证书
3. 运行 `docker-compose -f docker-compose.prod.yml up -d`

### 环境变量
确保在生产环境中设置正确的环境变量，特别是：
- 数据库密码
- JWT 密钥
- API 端点 URL

## 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
