version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: pet-store-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: wordpress
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - pet-store-network

  # WordPress Backend (Headless CMS)
  wordpress:
    image: wordpress:6.4-php8.2-fpm
    container_name: pet-store-wordpress
    restart: unless-stopped
    depends_on:
      - mysql
    environment:
      WORDPRESS_DB_HOST: mysql:3306
      WORDPRESS_DB_NAME: wordpress
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_DEBUG', true);
        define('WP_DEBUG_LOG', true);
        define('CORS_ALLOW_CREDENTIALS', true);
        define('JWT_AUTH_SECRET_KEY', 'your-secret-key-here');
    volumes:
      - wordpress_data:/var/www/html
      - ./wordpress/themes:/var/www/html/wp-content/themes
      - ./wordpress/plugins:/var/www/html/wp-content/plugins
      - ./wordpress/uploads:/var/www/html/wp-content/uploads
      - ./wordpress/php.ini:/usr/local/etc/php/conf.d/custom.ini
    networks:
      - pet-store-network

  # Nginx Web Server
  nginx:
    image: nginx:1.25-alpine
    container_name: pet-store-nginx
    restart: unless-stopped
    depends_on:
      - wordpress
      - nextjs
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/sites-available:/etc/nginx/sites-available
      - ./nginx/ssl:/etc/nginx/ssl
      - wordpress_data:/var/www/html
    networks:
      - pet-store-network

  # Next.js Frontend
  nextjs:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: pet-store-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_WORDPRESS_API_URL=http://nginx/wp-json
      - NEXT_PUBLIC_WORDPRESS_GRAPHQL_URL=http://nginx/graphql
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    ports:
      - "3000:3000"
    networks:
      - pet-store-network
    command: npm run dev

  # Redis for Caching (Optional)
  redis:
    image: redis:7-alpine
    container_name: pet-store-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pet-store-network

volumes:
  mysql_data:
  wordpress_data:
  redis_data:

networks:
  pet-store-network:
    driver: bridge
