; PHP Configuration for WordPress
upload_max_filesize = 64M
post_max_size = 64M
memory_limit = 256M
max_execution_time = 300
max_input_vars = 3000

; Error reporting
display_errors = On
display_startup_errors = On
log_errors = On
error_log = /var/log/php_errors.log

; Session settings
session.cookie_httponly = 1
session.cookie_secure = 0
session.use_strict_mode = 1

; Security settings
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Performance settings
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
