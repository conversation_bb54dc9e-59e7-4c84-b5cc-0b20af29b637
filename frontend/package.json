{"name": "pet-ecommerce-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "@apollo/client": "^3.11.4", "graphql": "^16.9.0", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.5", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0", "framer-motion": "^11.3.8", "react-hook-form": "^7.52.1", "@hookform/resolvers": "^3.9.0", "zod": "^3.23.8", "axios": "^1.7.2", "swr": "^2.2.5", "react-query": "^3.39.3", "zustand": "^4.5.4", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.13.0", "sharp": "^0.33.4", "next-themes": "^0.3.0"}, "devDependencies": {"@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "typescript": "^5.5.4", "tailwindcss": "^3.4.7", "postcss": "^8.4.40", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "jest": "^29.7.0", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.4.8", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}