# Pet E-commerce Store Makefile
.PHONY: help build up down restart logs clean install dev test

# Default target
help: ## Show this help message
	@echo "Pet E-commerce Store - Available commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Docker commands
build: ## Build all Docker containers
	docker-compose build --no-cache

up: ## Start all services
	docker-compose up -d

down: ## Stop all services
	docker-compose down

restart: ## Restart all services
	docker-compose restart

logs: ## Show logs for all services
	docker-compose logs -f

logs-frontend: ## Show logs for frontend service
	docker-compose logs -f nextjs

logs-wordpress: ## Show logs for WordPress service
	docker-compose logs -f wordpress

logs-nginx: ## Show logs for Nginx service
	docker-compose logs -f nginx

logs-mysql: ## Show logs for MySQL service
	docker-compose logs -f mysql

# Development commands
dev: ## Start development environment
	docker-compose up -d mysql wordpress nginx redis
	cd frontend && npm run dev

install: ## Install frontend dependencies
	cd frontend && npm install

install-frontend: ## Install frontend dependencies in container
	docker-compose exec nextjs npm install

# Database commands
db-backup: ## Backup MySQL database
	docker-compose exec mysql mysqldump -u wordpress -pwordpress wordpress > backup_$(shell date +%Y%m%d_%H%M%S).sql

db-restore: ## Restore MySQL database (usage: make db-restore FILE=backup.sql)
	docker-compose exec -T mysql mysql -u wordpress -pwordpress wordpress < $(FILE)

# WordPress commands
wp-cli: ## Access WordPress CLI
	docker-compose exec wordpress wp --allow-root

wp-install: ## Install WordPress (run after first startup)
	docker-compose exec wordpress wp --allow-root core install \
		--url=http://localhost \
		--title="Pet Store" \
		--admin_user=admin \
		--admin_password=admin123 \
		--admin_email=<EMAIL>

wp-plugins: ## Install required WordPress plugins
	docker-compose exec wordpress wp --allow-root plugin install woocommerce --activate
	docker-compose exec wordpress wp --allow-root plugin install wp-graphql --activate
	docker-compose exec wordpress wp --allow-root plugin install wp-graphql-woocommerce --activate
	docker-compose exec wordpress wp --allow-root plugin install jwt-authentication-for-wp-rest-api --activate

# Cleanup commands
clean: ## Clean up Docker containers and volumes
	docker-compose down -v
	docker system prune -f

clean-all: ## Clean up everything including images
	docker-compose down -v --rmi all
	docker system prune -af

# Testing commands
test: ## Run frontend tests
	cd frontend && npm test

test-watch: ## Run frontend tests in watch mode
	cd frontend && npm run test:watch

test-coverage: ## Run frontend tests with coverage
	cd frontend && npm run test:coverage

# Linting and formatting
lint: ## Run ESLint
	cd frontend && npm run lint

format: ## Format code with Prettier
	cd frontend && npx prettier --write .

type-check: ## Run TypeScript type checking
	cd frontend && npm run type-check

# Production commands
prod-build: ## Build for production
	docker-compose -f docker-compose.prod.yml build

prod-up: ## Start production environment
	docker-compose -f docker-compose.prod.yml up -d

prod-down: ## Stop production environment
	docker-compose -f docker-compose.prod.yml down

# SSL commands
ssl-generate: ## Generate self-signed SSL certificates
	mkdir -p nginx/ssl
	openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
		-keyout nginx/ssl/nginx.key \
		-out nginx/ssl/nginx.crt \
		-subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Status and monitoring
status: ## Show status of all services
	docker-compose ps

health: ## Check health of all services
	@echo "Checking service health..."
	@curl -f http://localhost:3000 > /dev/null 2>&1 && echo "✅ Frontend: OK" || echo "❌ Frontend: FAIL"
	@curl -f http://localhost/wp-json/wp/v2 > /dev/null 2>&1 && echo "✅ WordPress API: OK" || echo "❌ WordPress API: FAIL"
	@docker-compose exec mysql mysqladmin -u wordpress -pwordpress ping > /dev/null 2>&1 && echo "✅ MySQL: OK" || echo "❌ MySQL: FAIL"

# Quick setup
setup: ## Quick setup for new installation
	@echo "Setting up Pet E-commerce Store..."
	cp frontend/.env.local.example frontend/.env.local
	docker-compose up -d
	@echo "Waiting for services to start..."
	sleep 30
	make wp-install
	make wp-plugins
	@echo "Setup complete! Visit http://localhost:3000"

# Update commands
update: ## Update all dependencies
	cd frontend && npm update
	docker-compose pull
